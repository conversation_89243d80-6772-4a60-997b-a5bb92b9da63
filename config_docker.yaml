mongodb:
  enable: true
  db_host: sht_mongodb
  db_port: 27017
  connection_string: "mongodb+srv://"
  use_conn_str: false
mysql:
  enable: true
  host: sht_mysql
  port: 3306
  user: root
  password: "root"
  db: sehuatang

sehuatang:
  # 域名
  domain_name: sehuatang.org
  # 板块id
  fid:
#    - 103
    - 104
    - 37
    - 36
#    - 39
#    - 160
#    - 151

  # 抓取页数，10代表1-10页
  page_num: 5
  # 抓取日期(格式: yyyy-mm-dd), 如果为空, 则抓取当前日期，支持正则匹配，可输入 yyyy-mm 抓取指定月份的所有日期
  date:

proxy:
  # 代理服务器
  proxy_host: http://127.0.0.1:11223
  # 是否使用代理
  proxy_enable: false

sendMessage:
  # 是否发送消息
  send_wecom_enable: false
  # 企业ID
  corp_id:
  # 应用的凭证密钥
  corp_secret:
  # 应用ID
  agent_id:
  # mpnews消息类型页面图片media_id，需要提前将图片上传到企业微信素材库，接口返回media_id，不填则默认发文本消息
  media_id:
  # 接收的用户，多个用户用|分隔(如: use1|user2|user3)
  to_user: "@all"
  # telegram 推送
  send_telegram_enable: false
  tg_bot_token:
  tg_chat_id:

# 定时任务
schedule_time:
  - 01:00
  - 02:00
  - 03:00
  - 04:00


