name: 🐳 简化Docker构建

on:
  # push:
  #   branches: [ main, master, v2 ]
  #   tags: [ 'v*' ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

env:
  REGISTRY: docker.io
  IMAGE_NAME: cxsz16888/sehuatang

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
      
      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: 登录Docker Hub
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      
      - name: 提取元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=raw,value=latest,enable={{is_default_branch}}
          labels: |
            org.opencontainers.image.title=Sehuatang Crawler
            org.opencontainers.image.description=高效的论坛数据抓取系统
      
      - name: 构建并推送镜像
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/amd64,linux/arm64
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
      
      - name: 测试镜像
        if: github.event_name != 'pull_request'
        run: |
          # 创建测试配置
          mkdir -p test-config
          cp config/config.example.yaml test-config/config.yaml
          
          # 运行健康检查
          docker run --rm \
            -v $(pwd)/test-config:/app/config \
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest \
            python run.py --mode health
